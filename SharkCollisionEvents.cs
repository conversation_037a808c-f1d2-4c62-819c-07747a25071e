using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class SharkCollisionEvents : MonoBehaviour
{
    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Boat"))
        {
            FindObjectOfType<AudioManager>().PlaySound("Eat");
            if (GameManager.Score - 1 >= 0)
            {
                GameManager.Score -= 1;
                //Vibrate
                Handheld.Vibrate();
                //Shake Boat
                other.GetComponent<BoatShake>().Shake();
                CameraShake.Shake(1f, 0.7f);
                other.GetComponent<RescueePositionController>().RemoveRescuee();
            }
            else
            {
                other.GetComponent<Animator>().SetTrigger("CollidedWithIsland");
                StartCoroutine(WaitForIt(1.0F));
            }
            //Shake Camera
        }
        IEnumerator WaitForIt(float waitTime)
        {
            yield return new WaitForSeconds(waitTime);
            GameManager.gameOver = true;
        }
    }
}
