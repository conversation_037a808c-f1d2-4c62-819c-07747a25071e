using UnityEngine.SceneManagement;
using UnityEngine;
using UnityEngine.UI;

public class Events : MonoBehaviour
{
    public GameObject gameOverText;
    public Text speedText;
    public GameObject NextLevelText;
    public GameObject ProgressBar;
    public GameObject gameOverPanel;
    public GameObject NextLevel;

    public void Awake()
    {

        gameOverPanel.SetActive(false);
    }

    public void ReplayGame()
    {

        SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex);
    }

    public void QuitGame()
    {

        Application.Quit();
    }
    public void Level()
    {
        SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex + 1);
    }

    private void HideUIElements()
    {
        this.gameOverText.gameObject.SetActive(false);
        this.speedText.gameObject.SetActive(false);


    }

    public void UnhideUIElements()
    {
        this.gameOverText.gameObject.SetActive(true);
        this.speedText.gameObject.SetActive(true);
        
    }





    public void OnSkipBtnClick()
    {

        this.gameOverPanel.SetActive(true);

    }

    public void UnhideGameOverPanel()
    {
        this.gameOverPanel.SetActive(true);
        this.ProgressBar.gameObject.SetActive(false);
    }

    public void HideGameOverPanel()
    {
        this.gameOverPanel.SetActive(false);
    }
    public void UnhideNextLevel()
    {
        this.NextLevel.SetActive(true);
        this.ProgressBar.gameObject.SetActive(false);
    }

    public void HideNextLevell()
    {
        this.NextLevel.SetActive(false);
    }
    public void TapToPlay()
    {
    SceneManager.LoadScene("Cinematic");
    }


}
