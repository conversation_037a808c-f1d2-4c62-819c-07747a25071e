using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ProgressBar : MonoBehaviour
{
    [SerializeField] GameObject Intiate;
    [SerializeField] GameObject Finish;
    Image progressBar;
    float maxDistance;
    void Start()
    {
        progressBar = GetComponent<Image>();
        maxDistance = Finish.transform.position.z;
    }

    // Update is called once per frame
    void Update()
    {
        if(progressBar.fillAmount<1)
        {
            progressBar.fillAmount = Intiate.transform.position.z / maxDistance;
        }    
    }
}
