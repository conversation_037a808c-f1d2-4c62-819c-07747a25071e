using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class RescueePositionController : MonoBehaviour
{
    [SerializeField] Transform[] rescueeSit;
    private int sitIndex = 0;
    public void SaveRescuee(GameObject rescuee)
    {
        if (sitIndex >= rescueeSit.Length)
            return;
        rescuee.transform.localScale = new Vector3(1, 1, 1);
        rescuee.transform.position = rescueeSit[sitIndex].position;
        rescuee.transform.rotation = rescueeSit[sitIndex].rotation;
        rescuee.transform.parent = rescueeSit[sitIndex].transform;
        sitIndex++;
    }

    //Remove Any Rescuee from Boat
    public void RemoveRescuee()
    {
        if (sitIndex <= 0)
            return;
        sitIndex--;
        rescueeSit[sitIndex].GetChild(0).position = new Vector3(0, -100, 0);
        rescueeSit[sitIndex].GetChild(0).parent = null;
        //Move Rescuee to the somwhere far
    }
    public void HideHoomans()
    {
        foreach (var item in rescueeSit)
        {
            item.gameObject.SetActive(false);
        }
    }
}
