using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public class GameManager : MonoBehaviour
{
    public static bool gameOver;
    public GameObject gameOverPanel;
    public GameObject NextLevel;
    public static bool nextLevel;
    public GameObject ProgressBar;

    public static bool isGameStarted;
    public GameObject startingText;
    public GameObject Player;

    public static int Score;
    public int timeOfGame;
    public float timer;

    public Text ScoreText;
    public Text timeText;
    public GameObject Timer;
    public GameObject Logo;

    bool alreadyDone = false;



    void Start()
    {
        timer = 0.0f;
        timeOfGame = 0;



        gameOver = false;
        nextLevel = false;
        Time.timeScale = 1;
        isGameStarted = false;
        Score = 0;
    }
    void UpdateTime()
    {

        if (isGameStarted)
        {
            timer += Time.deltaTime;
            timeOfGame = Convert.ToInt32(timer);
        }

    }
    string FormatTimeText()
    {
        return (timeOfGame.ToString()).PadLeft(3, ' ') + "s";
    }
    private void Update()
    {
        if (gameOver)
        {

            if (!alreadyDone)
            {
                Events eventsObject = FindObjectOfType<Events>();
                Time.timeScale = 0;
                eventsObject.UnhideGameOverPanel();
                
            }
        }
        if (nextLevel)
        {
            Time.timeScale = 0;
            if (!alreadyDone)
            {
                Events eventsObject = FindObjectOfType<Events>();

                eventsObject.UnhideNextLevel();
            }
        }

        UpdateTime();
        ScoreText.text = "" + Score;
        timeText.text = "" + FormatTimeText();
        StartCoroutine(StartGame());
    }
    private IEnumerator StartGame()
    {
        if (SwipeManager.tap)
        {
            if (!isGameStarted)
            {
                
                

                yield return new WaitForSeconds(1);

                isGameStarted = true;

                Destroy(startingText);
                Destroy(Logo);


            }
        }
    }
}
