using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;

public class Filler : MonoBehaviour
{


    [SerializeField] GameObject playerGO;
    [SerializeField] GameObject finishGO;

    Image progressBar;
    float maxDistance;
    public void Start()
    {
        progressBar = GetComponent<Image>();
        maxDistance = -finishGO.transform.position.z;
        progressBar.fillAmount = -playerGO.transform.position.z / maxDistance;
    }
    public void Update()
    {
        if (progressBar.fillAmount < 1)
        {
            progressBar.fillAmount = -playerGO.transform.position.z / maxDistance;
        }
    }

}
